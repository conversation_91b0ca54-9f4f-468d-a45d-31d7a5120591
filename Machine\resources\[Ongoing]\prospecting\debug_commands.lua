-- Debug commands untuk troubleshooting prospecting system

QBCore, ESX = nil, nil

if Config.Core == "QBCore" then
    QBCore = exports['qb-core']:GetCoreObject()
elseif Config.Core == "ESX" then
    ESX = exports['r_core']:getSharedObject()
end

-- Command untuk cek status prospecting targets
RegisterCommand("checkprospecting", function(source, args, rawCommand)
    local src = source
    if src == 0 then -- Server console
        print("^2[PROSPECTING DEBUG]^7 Total targets in server: " .. #PROSPECTING_TARGETS)
        for i, target in pairs(PROSPECTING_TARGETS) do
            print(string.format("^3Target %d:^7 x=%.2f, y=%.2f, z=%.2f, data=%s", i, target.x, target.y, target.z, target.data))
        end
        return
    end
    
    -- Player command
    local playerName = GetPlayerName(src)
    print(string.format("^2[PROSPECTING DEBUG]^7 Player %s requested debug info", playerName))
    print(string.format("^2[PROSPECTING DEBUG]^7 Total targets in server: %d", #PROSPECTING_TARGETS))
    print(string.format("^2[PROSPECTING DEBUG]^7 Player prospecting status: %s", PROSPECTING_STATUS[src] and "ACTIVE" or "INACTIVE"))
    
    -- Send targets to player
    UpdateProspectingTargets(src)
    
    -- Notify player
    if Config.Core == "QBCore" then
        TriggerClientEvent('QBCore:Notify', src, 'Debug info printed to server console', 'primary')
    elseif Config.Core == "ESX" then
        TriggerClientEvent('esx:showNotification', src, 'Debug info printed to server console')
    end
end, false)

-- Command untuk force regenerate targets
RegisterCommand("regenprospecting", function(source, args, rawCommand)
    local src = source
    if src ~= 0 then -- Only server console
        print("Command hanya bisa digunakan dari server console")
        return
    end
    
    -- Clear existing targets
    PROSPECTING_TARGETS = {}
    
    -- Regenerate targets
    print("^2[PROSPECTING DEBUG]^7 Regenerating mining locations...")
    for _, v in pairs(Config.Zones) do
        local function GenerateCoords(coords, data, zoneSize, zoneLocations)
            local coordslist = {}
            local minDistance = 10.0
            local attempts = 0
            local maxAttempts = zoneLocations * 10

            while #coordslist < zoneLocations and attempts < maxAttempts do
                attempts += 1

                local modX = math.random(-zoneSize, zoneSize)
                local modY = math.random(-zoneSize, zoneSize)
                local coordX = coords.x + modX
                local coordY = coords.y + modY
                local coordZ = coords.z
                local newCoord = vector3(coordX, coordY, coordZ)

                local tooClose = false
                for i = 1, #coordslist do
                    local existing = coordslist[i]
                    if #(newCoord - vector3(existing.x, existing.y, existing.z)) < minDistance then
                        tooClose = true
                        break
                    end
                end

                if not tooClose then
                    coordslist[#coordslist + 1] = {
                        x = newCoord.x,
                        y = newCoord.y,
                        z = newCoord.z,
                        data = data
                    }
                end
            end

            -- Add to global targets
            for _, target in pairs(coordslist) do
                PROSPECTING_TARGETS[#PROSPECTING_TARGETS + 1] = {
                    resource = "prospecting", 
                    data = target.data, 
                    x = target.x, 
                    y = target.y, 
                    z = target.z
                }
            end
        end
        
        GenerateCoords(v.coords, v.data, v.zoneSize, v.zoneLocations)
        print(string.format("^2[PROSPECTING DEBUG]^7 Generated %d locations for zone: %s", v.zoneLocations, v.data))
    end
    print(string.format("^2[PROSPECTING DEBUG]^7 Total targets regenerated: %d", #PROSPECTING_TARGETS))
end, true)

-- Command untuk teleport ke mining zone
RegisterCommand("tpmining", function(source, args, rawCommand)
    local src = source
    if src == 0 then
        print("Command hanya bisa digunakan oleh player")
        return
    end
    
    local coords = Config.Zones[1].coords
    local ped = GetPlayerPed(src)
    SetEntityCoords(ped, coords.x, coords.y, coords.z + 1.0)
    
    if Config.Core == "QBCore" then
        TriggerClientEvent('QBCore:Notify', src, 'Teleported to mining zone!', 'success')
    elseif Config.Core == "ESX" then
        TriggerClientEvent('esx:showNotification', src, 'Teleported to mining zone!')
    end
end, false)

-- Command untuk cek detector yang dimiliki player
RegisterCommand("checkdetector", function(source, args, rawCommand)
    local src = source
    if src == 0 then
        print("Command hanya bisa digunakan oleh player")
        return
    end
    
    local detectorLevel = GetPlayerDetectorLevel(src)
    local playerName = GetPlayerName(src)
    
    print(string.format("^2[PROSPECTING DEBUG]^7 Player %s detector level: %d", playerName, detectorLevel))
    
    if Config.Core == "QBCore" then
        TriggerClientEvent('QBCore:Notify', src, 'Your detector level: ' .. detectorLevel, 'primary')
    elseif Config.Core == "ESX" then
        TriggerClientEvent('esx:showNotification', src, 'Your detector level: ' .. detectorLevel)
    end
end, false)

print("^2[PROSPECTING]^7 Debug commands loaded:")
print("^3/checkprospecting^7 - Check prospecting status and targets")
print("^3/regenprospecting^7 - Regenerate mining locations (server console only)")
print("^3/tpmining^7 - Teleport to mining zone")
print("^3/checkdetector^7 - Check your detector level")
