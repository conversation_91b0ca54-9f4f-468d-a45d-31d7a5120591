-- Test commands untuk memberikan detector items
-- Tambahkan ke server.cfg: ensure prospecting
-- Atau jalankan command ini di server console

QBCore, ESX = nil, nil

if Config.Core == "QBCore" then
    QBCore = exports['qb-core']:GetCoreObject()
elseif Config.Core == "ESX" then
    ESX = exports['r_core']:getSharedObject()
end

-- Command untuk memberikan detector level 1
RegisterCommand("givedetector1", function(source, args, rawCommand)
    local src = source
    if src == 0 then -- Server console
        print("Command hanya bisa digunakan oleh player")
        return
    end

    if Config.Core == "QBCore" then
        local Player = QBCore.Functions.GetPlayer(src)
        if Player then
            Player.Functions.AddItem("detector1", 1)
            TriggerClientEvent("inventory:client:ItemBox", src, QBCore.Shared.Items["detector1"], "add")
            TriggerClientEvent('QBCore:Notify', src, '<PERSON>mu mendapat Basic Detector!', 'success')
        end
    elseif Config.Core == "ESX" then
        local xPlayer = ESX.GetPlayerFromId(src)
        if xPlayer then
            xPlayer.addInventoryItem("detector1", 1)
            TriggerClientEvent('esx:showNotification', src, 'Kamu mendapat Basic Detector!')
        end
    end
end, false)

-- Command untuk memberikan detector level 2
RegisterCommand("givedetector2", function(source, args, rawCommand)
    local src = source
    if src == 0 then
        print("Command hanya bisa digunakan oleh player")
        return
    end

    if Config.Core == "QBCore" then
        local Player = QBCore.Functions.GetPlayer(src)
        if Player then
            Player.Functions.AddItem("detector2", 1)
            TriggerClientEvent("inventory:client:ItemBox", src, QBCore.Shared.Items["detector2"], "add")
            TriggerClientEvent('QBCore:Notify', src, 'Kamu mendapat Advanced Detector!', 'success')
        end
    elseif Config.Core == "ESX" then
        local xPlayer = ESX.GetPlayerFromId(src)
        if xPlayer then
            xPlayer.addInventoryItem("detector2", 1)
            TriggerClientEvent('esx:showNotification', src, 'Kamu mendapat Advanced Detector!')
        end
    end
end, false)

-- Command untuk memberikan detector level 3
RegisterCommand("givedetector3", function(source, args, rawCommand)
    local src = source
    if src == 0 then
        print("Command hanya bisa digunakan oleh player")
        return
    end

    if Config.Core == "QBCore" then
        local Player = QBCore.Functions.GetPlayer(src)
        if Player then
            Player.Functions.AddItem("detector3", 1)
            TriggerClientEvent("inventory:client:ItemBox", src, QBCore.Shared.Items["detector3"], "add")
            TriggerClientEvent('QBCore:Notify', src, 'Kamu mendapat Professional Detector!', 'success')
        end
    elseif Config.Core == "ESX" then
        local xPlayer = ESX.GetPlayerFromId(src)
        if xPlayer then
            xPlayer.addInventoryItem("detector3", 1)
            TriggerClientEvent('esx:showNotification', src, 'Kamu mendapat Professional Detector!')
        end
    end
end, false)

-- Command untuk memberikan detector level 4
RegisterCommand("givedetector4", function(source, args, rawCommand)
    local src = source
    if src == 0 then
        print("Command hanya bisa digunakan oleh player")
        return
    end

    if Config.Core == "QBCore" then
        local Player = QBCore.Functions.GetPlayer(src)
        if Player then
            Player.Functions.AddItem("detector4", 1)
            TriggerClientEvent("inventory:client:ItemBox", src, QBCore.Shared.Items["detector4"], "add")
            TriggerClientEvent('QBCore:Notify', src, 'Kamu mendapat Expert Detector!', 'success')
        end
    elseif Config.Core == "ESX" then
        local xPlayer = ESX.GetPlayerFromId(src)
        if xPlayer then
            xPlayer.addInventoryItem("detector4", 1)
            TriggerClientEvent('esx:showNotification', src, 'Kamu mendapat Expert Detector!')
        end
    end
end, false)

-- Command untuk memberikan detector level 5
RegisterCommand("givedetector5", function(source, args, rawCommand)
    local src = source
    if src == 0 then
        print("Command hanya bisa digunakan oleh player")
        return
    end

    if Config.Core == "QBCore" then
        local Player = QBCore.Functions.GetPlayer(src)
        if Player then
            Player.Functions.AddItem("detector5", 1)
            TriggerClientEvent("inventory:client:ItemBox", src, QBCore.Shared.Items["detector5"], "add")
            TriggerClientEvent('QBCore:Notify', src, 'Kamu mendapat Master Detector!', 'success')
        end
    elseif Config.Core == "ESX" then
        local xPlayer = ESX.GetPlayerFromId(src)
        if xPlayer then
            xPlayer.addInventoryItem("detector5", 1)
            TriggerClientEvent('esx:showNotification', src, 'Kamu mendapat Master Detector!')
        end
    end
end, false)

-- Command untuk memberikan semua detector sekaligus
RegisterCommand("givealldetectors", function(source, args, rawCommand)
    local src = source
    if src == 0 then
        print("Command hanya bisa digunakan oleh player")
        return
    end

    local detectors = {
        "detector1",
        "detector2",
        "detector3",
        "detector4",
        "detector5"
    }

    if Config.Core == "QBCore" then
        local Player = QBCore.Functions.GetPlayer(src)
        if Player then
            for _, detector in pairs(detectors) do
                Player.Functions.AddItem(detector, 1)
                TriggerClientEvent("inventory:client:ItemBox", src, QBCore.Shared.Items[detector], "add")
            end
            TriggerClientEvent('QBCore:Notify', src, 'Kamu mendapat semua detector!', 'success')
        end
    elseif Config.Core == "ESX" then
        local xPlayer = ESX.GetPlayerFromId(src)
        if xPlayer then
            for _, detector in pairs(detectors) do
                xPlayer.addInventoryItem(detector, 1)
            end
            TriggerClientEvent('esx:showNotification', src, 'Kamu mendapat semua detector!')
        end
    end
end, false)

print("^2[PROSPECTING]^7 Test commands loaded:")
print("^3/givedetector1^7 - Basic Detector")
print("^3/givedetector2^7 - Advanced Detector")
print("^3/givedetector3^7 - Professional Detector")
print("^3/givedetector4^7 - Expert Detector")
print("^3/givedetector5^7 - Master Detector")
print("^3/givealldetectors^7 - Semua detector sekaligus")
