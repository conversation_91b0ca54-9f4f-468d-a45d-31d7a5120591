-----------------For support, scripts, and more----------------
--------------- https://discord.gg/VGYkkAYVv2  -------------
---------------------------------------------------------------

Config = {}

Config.Core = "ESX" -- ESX or QBCore
Config.PlayerLoadedEvent = "esx:playerLoaded" -- esx:playerLoaded || QBCore:Client:OnPlayerLoaded

Config.ShowBlip = true -- show blip on map

Config.Chances = {
    ["common"] = 20, -- 60% chance
    ["rare"] = 20, -- 30% chance
    ["epic"] = 20, -- 10% chance
}
Config.ShowDrawMaker = true -- show draw marker on in game while prospecting

Config.DetectorItem = {
    "detector1",
    "detector2",
    "detector3",
    "detector4",
    "detector5"
}

Config.Zones = {
    [1] = {coords = vector3(2139.9097, 3835.6353, 30.3901), data = "mining_zone", zoneSize = 150, zoneLocations = 300},
}

Config.DefaultItems = {
    [1] = {name = "steel", min = 1, max = 2}
} -- will be selected if you dont put the common, rare and epic items in the config

-- Items configuration based on detector level
Config.Items = {
    ["mining_zone"] = {
        -- Level 1 Detector Rewards
        ["level1"] = {
            ["common"] = {
                [1] = {name = "paku", min = 100, max = 100},
                [2] = {name = "tutupbotol", min = 50, max = 50},
                [3] = {name = "besikarat", min = 250, max = 250},
                [4] = {name = "smallmetal", min = 1, max = 3},
                [5] = {name = "rustycoin", min = 1, max = 3},
            },
            ["rare"] = {
            },
            ["epic"] = {
            }
        },
        -- Level 2 Detector Rewards
        ["level2"] = {
            ["common"] = {
                [1] = {name = "paku", min = 100, max = 100},
                [2] = {name = "tutupbotol", min = 50, max = 50},
                [3] = {name = "besikarat", min = 250, max = 250},
                [4] = {name = "smallmetal", min = 1, max = 3},
                [5] = {name = "rustycoin", min = 1, max = 3},
                [6] = {name = "oldcoin", min = 1, max = 3},
            },
            ["rare"] = {
            },
            ["epic"] = {
            }
        },
        -- Level 3 Detector Rewards
        ["level3"] = {
            ["common"] = {
                [1] = {name = "paku", min = 100, max = 100},
                [2] = {name = "tutupbotol", min = 50, max = 50},
                [3] = {name = "besikarat", min = 250, max = 250},
                [4] = {name = "smallmetal", min = 1, max = 3},
                [5] = {name = "rustycoin", min = 1, max = 3},
                [6] = {name = "oldcoin", min = 1, max = 3},
            },
            ["rare"] = {
                [1] = {name = "rarecoin", min = 1, max = 3},
                [2] = {name = "silverring", min = 1, max = 1},
                [3] = {name = "silverwacth", min = 1, max = 1},
                [4] = {name = "bp_de", min = 1, max = 1},
                [5] = {name = "bp_tec9", min = 1, max = 1},
                [6] = {name = "bp_minismg", min = 1, max = 1},
                [7] = {name = "bp_vest", min = 1, max = 1},

            },
            ["epic"] = {
            }
        },
        -- Level 4 Detector Rewards
        ["level4"] = {
            ["common"] = {
                [1] = {name = "paku", min = 100, max = 100},
                [2] = {name = "tutupbotol", min = 50, max = 50},
                [3] = {name = "besikarat", min = 250, max = 250},
                [4] = {name = "smallmetal", min = 1, max = 3},
                [5] = {name = "rustycoin", min = 1, max = 3},
                [6] = {name = "oldcoin", min = 1, max = 3},
            },
            ["rare"] = {
                [1] = {name = "rarecoin", min = 1, max = 3},
                [2] = {name = "silverring", min = 1, max = 1},
                [3] = {name = "silverwacth", min = 1, max = 1},
                [4] = {name = "bp_de", min = 1, max = 1},
                [5] = {name = "bp_tec9", min = 1, max = 1},
                [6] = {name = "bp_minismg", min = 1, max = 1},
                [7] = {name = "bp_vest", min = 1, max = 1},

            },
            ["epic"] = {
                [1] = {name = "goldnecklane", min = 1, max = 1},
                [2] = {name = "ancientrelic", min = 1, max = 1},
                [3] = {name = "refinedmetal", min = 1, max = 1},
            }
        },
        -- Level 5 Detector Rewards (Master Level)
        ["level5"] = {
            ["common"] = {
                [1] = {name = "paku", min = 100, max = 100},
                [2] = {name = "tutupbotol", min = 50, max = 50},
                [3] = {name = "besikarat", min = 250, max = 250},
                [4] = {name = "smallmetal", min = 1, max = 3},
                [5] = {name = "rustycoin", min = 1, max = 3},
                [6] = {name = "oldcoin", min = 1, max = 3},
            },
            ["rare"] = {
                [1] = {name = "rarecoin", min = 1, max = 3},
                [2] = {name = "silverring", min = 1, max = 1},
                [3] = {name = "silverwacth", min = 1, max = 1},
                [4] = {name = "bp_de", min = 1, max = 1},
                [5] = {name = "bp_tec9", min = 1, max = 1},
                [6] = {name = "bp_minismg", min = 1, max = 1},
                [7] = {name = "bp_vest", min = 1, max = 1},

            },
            ["epic"] = {
                [1] = {name = "goldnecklane", min = 1, max = 1},
                [2] = {name = "ancientrelic", min = 1, max = 1},
                [3] = {name = "refinedmetal", min = 1, max = 1},
                [4] = {name = "legendarycoin", min = 1, max = 1},
            }
        }
    },
}
