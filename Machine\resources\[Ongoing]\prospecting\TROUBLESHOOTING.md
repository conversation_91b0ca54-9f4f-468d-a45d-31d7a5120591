# 🔧 Troubleshooting Guide - Prospecting System

## 🚨 Masalah: Marker Tidak Muncul

### 📋 **Langkah-langkah Debugging:**

#### **1. Cek Setup Dasar**
```bash
# 1. Pastikan resource berjalan
/restart prospecting

# 2. Cek console untuk error
# Lihat F8 console untuk error messages
```

#### **2. Cek Database Items**
```sql
-- Untuk ESX, pastikan items ada di database:
SELECT * FROM items WHERE name LIKE 'detector%';

-- Harus ada: detector1, detector2, detector3, detector4, detector5
-- Dan semua mining reward items: stone, sand, iron, copper, dll
```

#### **3. Test Commands untuk Debugging**
```bash
# Berikan detector untuk testing
/givedetector1          # Basic detector
/givealldetectors       # Semua detector sekaligus

# Debug commands
/checkprospecting       # Cek status targets dan prospecting
/tpmining              # Teleport ke mining zone
/checkdetector         # Cek level detector yang dimiliki
/regenprospecting      # Regenerate targets (server console only)
```

#### **4. Langkah Testing Sistematis**

**Step 1: Cek Targets Generation**
1. Restart server/resource
2. Tunggu 10 detik (untuk target generation)
3. Gunakan `/checkprospecting` - harus ada 300 targets
4. Jika tidak ada targets, gunakan `/regenprospecting` di server console

**Step 2: Cek Item Detection**
1. Gunakan `/givedetector1` untuk dapat detector
2. Gunakan `/checkdetector` - harus show level 1
3. Jika tidak detect, cek nama item di database/qb-core items

**Step 3: Cek Prospecting Activation**
1. Gunakan `/tpmining` untuk ke mining zone
2. Klik kanan detector di inventory atau gunakan `/prospect`
3. Cek console untuk debug messages:
   - "Starting prospecting..."
   - "Available targets in pool: X"
   - "Received X targets from server"

**Step 4: Cek Marker Display**
1. Pastikan `Config.ShowDrawMaker = true` di config.lua
2. Saat prospecting aktif, marker harus muncul sebagai lingkaran hijau
3. Jika tidak muncul, cek jarak ke targets (max 200 unit)

### 🔍 **Debug Messages yang Harus Muncul:**

#### **Server Console:**
```
[PROSPECTING] Generating mining locations...
[PROSPECTING] Generated 300 locations for zone: mining_zone
[PROSPECTING] Total targets generated: 300
[PROSPECTING] Sending X targets to player PlayerName
[PROSPECTING] Player PlayerName used level 1 detector, got 2x stone
```

#### **Client Console (F8):**
```
[PROSPECTING CLIENT] Received 300 targets from server
[PROSPECTING CLIENT] Starting prospecting...
[PROSPECTING CLIENT] Available targets in pool: 300
```

### ⚠️ **Common Issues & Solutions:**

#### **Issue 1: "No targets generated"**
**Solution:**
```bash
# Server console:
/regenprospecting

# Atau restart resource:
/restart prospecting
```

#### **Issue 2: "Detector tidak bisa digunakan"**
**Solution:**
1. Cek items di database (ESX) atau qb-core items.lua (QBCore)
2. Pastikan nama item sesuai: `detector1`, `detector2`, dll
3. Import `items.sql` ke database

#### **Issue 3: "Marker tidak muncul tapi prospecting aktif"**
**Solution:**
1. Cek `Config.ShowDrawMaker = true`
2. Pastikan berada dalam jarak 200 unit dari targets
3. Gunakan `/tpmining` untuk ke mining zone
4. Cek apakah ada targets nearby dengan `/checkprospecting`

#### **Issue 4: "Tidak dapat reward"**
**Solution:**
1. Cek inventory tidak penuh
2. Pastikan mining reward items ada di database
3. Cek ValidItems di `sv_prospecting.lua`

### 📊 **Expected Behavior:**

1. **Server Start**: 300 targets generated dalam 10 detik
2. **Player Join**: Targets dikirim ke client otomatis
3. **Use Detector**: Prospecting mode aktif, marker muncul
4. **Find Target**: Lingkaran hijau berubah warna saat dekat target
5. **Dig Target**: Dapat reward sesuai detector level

### 🛠️ **Manual Fix untuk Marker:**

Jika marker masih tidak muncul, coba edit `cl_prospecting.lua`:

```lua
-- Tambahkan debug print di line ~421:
if Config.ShowDrawMaker then
    print("Drawing marker at: " .. tostring(pos)) -- DEBUG
    circleSize = (circleScale % 100) / 100
    circleA = math.floor(255 - ((circleScale % 100) / 100) * 255)
    DrawMarker(1, pos, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, circleSize, circleSize, 0.1, circleR, circleG, circleB, circleA)
end
```

### 📞 **Jika Masih Bermasalah:**

1. **Kirim screenshot** console errors
2. **Kirim hasil** dari `/checkprospecting`
3. **Konfirmasi** framework (ESX/QBCore) dan versi
4. **Cek** apakah ada conflict dengan resource lain

### 🔄 **Quick Reset:**

```bash
# Complete reset procedure:
/stop prospecting
# Import items.sql ke database
/start prospecting
# Tunggu 10 detik
/givedetector1
/tpmining
/prospect
```
