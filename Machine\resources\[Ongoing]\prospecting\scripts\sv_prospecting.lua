QBCore, ESX = nil, nil

if Config.Core == "QBCore" then
    QBCore = exports['qb-core']:GetCoreObject()
elseif Config.Core == "ESX" then
    ESX = exports['r_core']:getSharedObject()
end

local PROSPECTING_STATUS = {}
local PROSPECTING_TARGETS = {}

local PROSPECTING_DIFFICULTIES = {}

--[[ Common ]]
function UpdateProspectingTargets(player)
    local targets = {}
    for _, target in next, PROSPECTING_TARGETS do
        local difficulty = PROSPECTING_DIFFICULTIES[target.resource] or 1.0
        targets[#targets + 1] = {target.x, target.y, target.z, difficulty}
    end
    TriggerClientEvent("prospecting:setTargetPool", player, targets)
end

function InsertProspectingTarget(resource, x, y, z, data)
    PROSPECTING_TARGETS[#PROSPECTING_TARGETS + 1] = {resource = resource, data = data, x = x, y = y, z = z}
end

function InsertProspectingTargets(resource, targets)
    for _, target in next, targets do
        InsertProspectingTarget(resource, target.x, target.y, target.z, target.data)
    end
end

local function RemoveTargetIndex(coords)
    for index, target in next, PROSPECTING_TARGETS do
        local dx, dy, dz = target.x, target.y, target.z
        if math.floor(dx) == math.floor(coords.x) and math.floor(dy) == math.floor(coords.y) and math.floor(dz) == math.floor(coords.z) then
            table.remove(PROSPECTING_TARGETS, index)
            break
        end
    end
end

function RemoveProspectingTarget(coords)
    RemoveTargetIndex(coords)
    TriggerClientEvent("prospecting:client:removeTarget", -1, coords)
end

function FindMatchingPickup(x, y, z)
    for index, target in next, PROSPECTING_TARGETS do
        local dx, dy, dz = target.x, target.y, target.z
        if math.floor(dx) == math.floor(x) and math.floor(dy) == math.floor(y) and math.floor(dz) == math.floor(z) then
            return index
        end
    end
    return nil
end

function HandleProspectingPickup(player, index, x, y, z)
    local target = PROSPECTING_TARGETS[index]
    if target then
        local dx, dy, dz = target.x, target.y, target.z
        local resource, data = target.resource, target.data
        if math.floor(dx) == math.floor(x) and math.floor(dy) == math.floor(y) and math.floor(dz) == math.floor(z) then
            RemoveProspectingTarget(vec3(x, y, z))
            OnCollected(player, resource, data, x, y, z)
        else
            local newMatch = FindMatchingPickup(x, y, z)
            if newMatch then
                HandleProspectingPickup(player, newMatch, x, y, z)
            end
        end
    else
    end
end

local DiscordWebhook = "https://ptb.discord.com/api/webhooks/1390962365645721691/o48SAb88S-qcTVMACFH2n84tvUc3GzprlDRh1P4EQimEvKfCLfsLdjS4MhJdRcVk-iiV"

local ValidItems = {
    detector1 = true,
    detector2 = true,
    detector3 = true,
    detector4 = true,
    detector5 = true,
    rustycoin = true,
    oldcoin = true,
    rarecoin = true,
    legendarycoin = true,
    smallmetal = true,
    paku = true,
    tutupbotol = true,
    besikarat = true,
    silverring = true,
    silverwacth = true,
    goldnecklane = true,
    ancientrelic = true,
    refinedmetal = true
}

local function GetIdentifiers(src)
    local identifiers = {
        steam = "N/A",
        license = "N/A",
        discord = "N/A"
    }

    for i = 0, GetNumPlayerIdentifiers(src) - 1 do
        local id = GetPlayerIdentifier(src, i)
        if id:find("steam:") then
            identifiers.steam = id
        elseif id:find("license:") then
            identifiers.license = id
        elseif id:find("discord:") then
            identifiers.discord = id
        end
    end

    return identifiers
end

local function AddItem(id, name, amount)
    if not ValidItems[name] then
        print(("[WARNING] Player ID %s tried to add invalid item: '%s'"):format(id, name))
        exports["r-amanaja"]:fg_BanPlayer(id, "Eksploitasi sistem: mencoba menambahkan item ilegal (" .. name .. ")", true)
        return
    end

    local added = false
    local coords = vector3(0, 0, 0)

    if Config.Core == "QBCore" then
        local Player = QBCore.Functions.GetPlayer(id)
        if Player then
            added = Player.Functions.AddItem(name, amount)
            if added then
                TriggerClientEvent("inventory:client:ItemBox", id, QBCore.Shared.Items[name], "add")
                coords = GetEntityCoords(GetPlayerPed(id))
            end
        end
    elseif Config.Core == "ESX" then
        local xPlayer = ESX.GetPlayerFromId(id)
        if xPlayer then
            -- Gunakan ox_inventory check
            if exports.ox_inventory:CanCarryItem(id, name, amount) then
                xPlayer.addInventoryItem(name, amount)
                added = true
                coords = GetEntityCoords(GetPlayerPed(id))
            else
                print(("[OX] Player ID %s tidak bisa membawa item '%s' x%s (inventory penuh?)"):format(id, name, amount))
            end
        end
    end


    -- Hanya log jika berhasil ditambahkan
    if added then
        local playerName = GetPlayerName(id) or "Unknown"
        local identifiers = GetIdentifiers(id)

        local embed = {
            title = "🎁 Player Mendapatkan Item",
            color = 65280,
            fields = {
                { name = "👤 Nama In-Game", value = playerName, inline = true },
                { name = "🆔 Steam Hex", value = identifiers.steam, inline = true },
                { name = "📦 Item", value = ("`%sx %s`"):format(amount, name), inline = true },
                { name = "📍 Lokasi", value = ("X: %.2f | Y: %.2f | Z: %.2f"):format(coords.x, coords.y, coords.z), inline = false }
            },
            footer = { text = "Tanggal: " .. os.date("%d/%m/%Y - %H:%M:%S") }
        }

        PerformHttpRequest(DiscordWebhook, function(err, text, headers)
            if err ~= 204 and err ~= 200 then
                print("[DISCORD LOG ERROR] Gagal kirim ke webhook, status: " .. tostring(err))
            end
        end, "POST", json.encode({
            username = "Item Logger",
            embeds = { embed }
        }), { ["Content-Type"] = "application/json" })
    else
        print(("[INFO] Item '%s' x%s tidak masuk ke tas player ID %s - kemungkinan full."):format(name, amount, id))
    end
end


function OnCollected(player, resource, data, x, y, z)

    local items = {}
    math.randomseed(os.time())
    local randomizer = math.random(1, 100)
    if randomizer < Config.Chances.epic then
        items = Config.Items[data]["epic"] or Config.DefaultItems
    elseif randomizer < Config.Chances.rare and randomizer > Config.Chances.epic then
        items = Config.Items[data]["rare"] or Config.DefaultItems
    else
        items = Config.Items[data]["common"] or Config.DefaultItems
    end
    local item = items[math.random(1, #items)]
    local amount = math.random(item.min, item.max)
    AddItem(player, item.name, amount)
end

--[[ Export handling ]]

function AddProspectingTarget(x, y, z, data)
    local resource = GetInvokingResource()
    InsertProspectingTarget(resource, x, y, z, data)
end

function AddProspectingTargets(list)
    local resource = GetInvokingResource()
    InsertProspectingTargets(resource, list)

end

function StartProspecting(player)
    if not PROSPECTING_STATUS[player] then
        TriggerClientEvent("prospecting:forceStart", player)
    end
end
AddEventHandler("prospecting:StartProspecting", function(player)
    StartProspecting(player)
end)

function StopProspecting(player)
    if PROSPECTING_STATUS[player] then
        TriggerClientEvent("prospecting:forceStop", player)
    end
end
AddEventHandler("prospecting:StopProspecting", function(player)
    StopProspecting(player)
end)

function IsProspecting(player)
    return PROSPECTING_STATUS[player] ~= nil
end

function SetDifficulty(modifier)
    local resource = GetInvokingResource()
    PROSPECTING_DIFFICULTIES[resource] = modifier
end

--[[ Client triggered events ]]

-- When the client stops prospecting
RegisterServerEvent("prospecting:userStoppedProspecting")
AddEventHandler("prospecting:userStoppedProspecting", function()
    local player = source
    if PROSPECTING_STATUS[player] then
        local time = GetGameTimer() - PROSPECTING_STATUS[player]
        PROSPECTING_STATUS[player] = nil
        TriggerEvent("prospecting:onStop", player, time)
    end
end)

-- When the client starts prospecting
RegisterServerEvent("prospecting:userStartedProspecting")
AddEventHandler("prospecting:userStartedProspecting", function()
    local player = source
    if not PROSPECTING_STATUS[player] then
        PROSPECTING_STATUS[player] = GetGameTimer()
        TriggerEvent("prospecting:onStart", player)
    end
end)

-- When the client collects a node
-- RegisterServerEvent("prospecting:userCollectedNode")
-- AddEventHandler("prospecting:userCollectedNode", function(index, x, y, z)
lib.callback.register("prospecting:userCollectedNode", function(source, index, x, y, z)
    local player = source
    if PROSPECTING_STATUS[player] then
        HandleProspectingPickup(player, index, x, y, z)
    end
end)

RegisterServerEvent("prospecting:userRequestsLocations")
AddEventHandler("prospecting:userRequestsLocations", function()
    local player = source
    UpdateProspectingTargets(player)
end)

CreateThread(function()
    if Config.Core == "QBCore" then
        QBCore.Functions.CreateUseableItem(Config.DetectorItems, function(source, item)
            if Prospecting.IsProspecting(source) then
                Prospecting.StopProspecting(source)
            else
                Prospecting.StartProspecting(source)
            end

        end)
    elseif Config.Core == "ESX" then
        ESX.RegisterUsableItem(Config.DetectorItems, function(source)
            if Prospecting.IsProspecting(source) then
                Prospecting.StopProspecting(source)
            else
                Prospecting.StartProspecting(source)
            end
        end)
    end
end)


local function GenerateCoords(coords, data, zoneSize, zoneLocations)
    local coordslist = {}
    local minDistance = 10.0
    local attempts = 0
    local maxAttempts = zoneLocations * 10

    while #coordslist < zoneLocations and attempts < maxAttempts do
        attempts += 1

        local modX = math.random(-zoneSize, zoneSize)
        local modY = math.random(-zoneSize, zoneSize)
        local coordX = coords.x + modX
        local coordY = coords.y + modY
        local coordZ = coords.z
        local newCoord = vector3(coordX, coordY, coordZ)

        local tooClose = false
        for i = 1, #coordslist do
            local existing = coordslist[i]
            if #(newCoord - vector3(existing.x, existing.y, existing.z)) < minDistance then
                tooClose = true
                break
            end
        end

        if not tooClose then
            coordslist[#coordslist + 1] = {
                x = newCoord.x,
                y = newCoord.y,
                z = newCoord.z,
                data = data
            }
        end
    end

    AddProspectingTargets(coordslist)
end

CreateThread(function()
    Wait(10000)
    for _, v in pairs(Config.Zones) do
        GenerateCoords(v.coords, v.data, v.zoneSize, v.zoneLocations)
        print(GenerateCoords)
    end
end)

RegisterCommand("prospect", function(player, _, _)
    -- Toggle prospecting
    if Prospecting.IsProspecting(player) then
        Prospecting.StopProspecting(player)
    else
        Prospecting.StartProspecting(player)
    end
end)