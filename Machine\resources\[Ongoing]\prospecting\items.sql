-- SQL untuk menambahkan detector items ke database
-- Untuk ESX (ox_inventory atau esx_inventory)

-- Detector Level 1 (Basic)
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES 
('detector_level1', 'Basic Detector', 500, 0, 1);

-- Detector Level 2 (Advanced)
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES 
('detector_level2', 'Advanced Detector', 600, 0, 1);

-- Detector Level 3 (Professional)
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES 
('detector_level3', 'Professional Detector', 700, 0, 1);

-- Detector Level 4 (Expert)
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES 
('detector_level4', 'Expert Detector', 800, 0, 1);

-- Detector Level 5 (Master)
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES 
('detector_level5', 'Master Detector', 1000, 0, 1);

-- Mining reward items
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES 
('stone', 'Stone', 50, 0, 1),
('sand', 'Sand', 30, 0, 1),
('iron', 'Iron Ore', 100, 0, 1),
('copper', 'Copper Ore', 120, 0, 1),
('steel', 'Steel', 150, 0, 1),
('silver', 'Silver Ore', 200, 0, 1),
('gold', 'Gold Ore', 300, 0, 1),
('titanium', 'Titanium Ore', 400, 0, 1),
('diamond', 'Diamond', 500, 1, 1),
('emerald', 'Emerald', 600, 1, 1),
('ruby', 'Ruby', 700, 1, 1),
('sapphire', 'Sapphire', 800, 1, 1);

-- Untuk QBCore, tambahkan ke qb-core/shared/items.lua:
--[[
-- Detector Items
['detector_level1'] = {['name'] = 'detector_level1', ['label'] = 'Basic Detector', ['weight'] = 500, ['type'] = 'item', ['image'] = 'detector_level1.png', ['unique'] = false, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Basic metal detector for prospecting'},
['detector_level2'] = {['name'] = 'detector_level2', ['label'] = 'Advanced Detector', ['weight'] = 600, ['type'] = 'item', ['image'] = 'detector_level2.png', ['unique'] = false, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Advanced metal detector with better detection'},
['detector_level3'] = {['name'] = 'detector_level3', ['label'] = 'Professional Detector', ['weight'] = 700, ['type'] = 'item', ['image'] = 'detector_level3.png', ['unique'] = false, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Professional grade metal detector'},
['detector_level4'] = {['name'] = 'detector_level4', ['label'] = 'Expert Detector', ['weight'] = 800, ['type'] = 'item', ['image'] = 'detector_level4.png', ['unique'] = false, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Expert level metal detector for rare finds'},
['detector_level5'] = {['name'] = 'detector_level5', ['label'] = 'Master Detector', ['weight'] = 1000, ['type'] = 'item', ['image'] = 'detector_level5.png', ['unique'] = false, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Master level detector for legendary treasures'},

-- Mining Reward Items
['stone'] = {['name'] = 'stone', ['label'] = 'Stone', ['weight'] = 50, ['type'] = 'item', ['image'] = 'stone.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = false, ['combinable'] = nil, ['description'] = 'Common stone'},
['sand'] = {['name'] = 'sand', ['label'] = 'Sand', ['weight'] = 30, ['type'] = 'item', ['image'] = 'sand.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = false, ['combinable'] = nil, ['description'] = 'Fine sand'},
['iron'] = {['name'] = 'iron', ['label'] = 'Iron Ore', ['weight'] = 100, ['type'] = 'item', ['image'] = 'iron.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = false, ['combinable'] = nil, ['description'] = 'Raw iron ore'},
['copper'] = {['name'] = 'copper', ['label'] = 'Copper Ore', ['weight'] = 120, ['type'] = 'item', ['image'] = 'copper.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = false, ['combinable'] = nil, ['description'] = 'Raw copper ore'},
['steel'] = {['name'] = 'steel', ['label'] = 'Steel', ['weight'] = 150, ['type'] = 'item', ['image'] = 'steel.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = false, ['combinable'] = nil, ['description'] = 'Processed steel'},
['silver'] = {['name'] = 'silver', ['label'] = 'Silver Ore', ['weight'] = 200, ['type'] = 'item', ['image'] = 'silver.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = false, ['combinable'] = nil, ['description'] = 'Precious silver ore'},
['gold'] = {['name'] = 'gold', ['label'] = 'Gold Ore', ['weight'] = 300, ['type'] = 'item', ['image'] = 'gold.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = false, ['combinable'] = nil, ['description'] = 'Precious gold ore'},
['titanium'] = {['name'] = 'titanium', ['label'] = 'Titanium Ore', ['weight'] = 400, ['type'] = 'item', ['image'] = 'titanium.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = false, ['combinable'] = nil, ['description'] = 'Rare titanium ore'},
['diamond'] = {['name'] = 'diamond', ['label'] = 'Diamond', ['weight'] = 500, ['type'] = 'item', ['image'] = 'diamond.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = false, ['combinable'] = nil, ['description'] = 'Precious diamond'},
['emerald'] = {['name'] = 'emerald', ['label'] = 'Emerald', ['weight'] = 600, ['type'] = 'item', ['image'] = 'emerald.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = false, ['combinable'] = nil, ['description'] = 'Rare emerald gemstone'},
['ruby'] = {['name'] = 'ruby', ['label'] = 'Ruby', ['weight'] = 700, ['type'] = 'item', ['image'] = 'ruby.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = false, ['combinable'] = nil, ['description'] = 'Legendary ruby gemstone'},
['sapphire'] = {['name'] = 'sapphire', ['label'] = 'Sapphire', ['weight'] = 800, ['type'] = 'item', ['image'] = 'sapphire.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = false, ['combinable'] = nil, ['description'] = 'Legendary sapphire gemstone'},
--]]
